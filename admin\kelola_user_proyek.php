<?php
// Session is already started in header component
require '../koneksi.php';

// Session validation is already handled in admin.php
// No need to check again since this file is included through admin routing

// Handle form submission
if ($_POST) {
    $action = $_POST['action'];
    
    if ($action == 'tambah') {
        $nama = mysqli_real_escape_string($koneksi, $_POST['nama_petugas']);
        $username = mysqli_real_escape_string($koneksi, $_POST['username']);
        $password = mysqli_real_escape_string($koneksi, $_POST['password']);
        
        // Cek username sudah ada atau belum
        $cek_username = mysqli_query($koneksi, "SELECT * FROM petugas WHERE username='$username'");
        if (mysqli_num_rows($cek_username) > 0) {
            echo "<script>alert('Username sudah digunakan!'); window.location='admin.php?url=kelola_user_proyek';</script>";
            exit;
        }
        
        $sql = "INSERT INTO petugas (nama_petugas, username, password, level) VALUES ('$nama', '$username', '$password', 'proyek')";
        if (mysqli_query($koneksi, $sql)) {
            echo "<script>alert('User proyek berhasil ditambahkan!'); window.location='admin.php?url=kelola_user_proyek';</script>";
        } else {
            echo "<script>alert('Gagal menambahkan user proyek!'); window.location='admin.php?url=kelola_user_proyek';</script>";
        }
    }
    
    if ($action == 'edit') {
        $id = $_POST['id_petugas'];
        $nama = mysqli_real_escape_string($koneksi, $_POST['nama_petugas']);
        $username = mysqli_real_escape_string($koneksi, $_POST['username']);
        $password = mysqli_real_escape_string($koneksi, $_POST['password']);
        
        // Cek username sudah ada atau belum (kecuali untuk user yang sedang diedit)
        $cek_username = mysqli_query($koneksi, "SELECT * FROM petugas WHERE username='$username' AND id_petugas != '$id'");
        if (mysqli_num_rows($cek_username) > 0) {
            echo "<script>alert('Username sudah digunakan!'); window.location='admin.php?url=kelola_user_proyek';</script>";
            exit;
        }
        
        $sql = "UPDATE petugas SET nama_petugas='$nama', username='$username', password='$password' WHERE id_petugas='$id'";
        if (mysqli_query($koneksi, $sql)) {
            echo "<script>alert('User proyek berhasil diupdate!'); window.location='admin.php?url=kelola_user_proyek';</script>";
        } else {
            echo "<script>alert('Gagal mengupdate user proyek!'); window.location='admin.php?url=kelola_user_proyek';</script>";
        }
    }
    
    if ($action == 'hapus') {
        $id = $_POST['id_petugas'];
        $sql = "DELETE FROM petugas WHERE id_petugas='$id'";
        if (mysqli_query($koneksi, $sql)) {
            echo "<script>alert('User proyek berhasil dihapus!'); window.location='admin.php?url=kelola_user_proyek';</script>";
        } else {
            echo "<script>alert('Gagal menghapus user proyek!'); window.location='admin.php?url=kelola_user_proyek';</script>";
        }
    }
}

// Get data for edit
$edit_data = null;
if (isset($_GET['edit'])) {
    $edit_id = $_GET['edit'];
    $edit_query = mysqli_query($koneksi, "SELECT * FROM petugas WHERE id_petugas='$edit_id' AND level='proyek'");
    $edit_data = mysqli_fetch_array($edit_query);
}
?>

<!-- Begin Page Content -->
<div class="container-fluid">

    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Kelola User Proyek</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0 bg-transparent p-0">
                <li class="breadcrumb-item"><a href="admin.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="#">Manajemen User</a></li>
                <li class="breadcrumb-item active">User Proyek</li>
            </ol>
        </nav>
    </div>

    <!-- Info Alert -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="fas fa-info-circle mr-2"></i>
        <strong>Informasi:</strong> Halaman ini digunakan untuk mengelola user yang memiliki akses ke modul proyek.
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <!-- Form Tambah/Edit User Proyek -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-<?php echo $edit_data ? 'edit' : 'plus-circle'; ?> mr-2"></i>
                        <?php echo $edit_data ? 'Edit User Proyek' : 'Tambah User Proyek'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if ($edit_data): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Mode Edit:</strong> Anda sedang mengedit data user "<?php echo htmlspecialchars($edit_data['nama_petugas']); ?>".
                    </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <input type="hidden" name="action" value="<?php echo $edit_data ? 'edit' : 'tambah'; ?>">
                        <?php if ($edit_data): ?>
                            <input type="hidden" name="id_petugas" value="<?php echo $edit_data['id_petugas']; ?>">
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="nama_petugas" class="font-weight-bold">Nama Petugas</label>
                                    <input type="text" class="form-control" id="nama_petugas" name="nama_petugas"
                                           value="<?php echo $edit_data ? htmlspecialchars($edit_data['nama_petugas']) : ''; ?>"
                                           placeholder="Masukkan nama lengkap petugas" required>
                                    <small class="form-text text-muted">Nama lengkap petugas yang akan menggunakan sistem</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="username" class="font-weight-bold">Username</label>
                                    <input type="text" class="form-control" id="username" name="username"
                                           value="<?php echo $edit_data ? htmlspecialchars($edit_data['username']) : ''; ?>"
                                           placeholder="Masukkan username untuk login" required>
                                    <small class="form-text text-muted">Username harus unik dan akan digunakan untuk login</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password" class="font-weight-bold">Password</label>
                                    <input type="password" class="form-control" id="password" name="password"
                                           value="<?php echo $edit_data ? $edit_data['password'] : ''; ?>"
                                           placeholder="Masukkan password" required>
                                    <small class="form-text text-muted">Password untuk login ke sistem</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="level" class="font-weight-bold">Level Akses</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="Proyek" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i class="fas fa-lock text-muted"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">Level akses tetap untuk modul proyek</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-4">
                            <div class="row">
                                <div class="col-sm-6 mb-2">
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-<?php echo $edit_data ? 'save' : 'plus'; ?> mr-2"></i>
                                        <?php echo $edit_data ? 'Update User' : 'Tambah User'; ?>
                                    </button>
                                </div>
                                <div class="col-sm-6 mb-2">
                                    <?php if ($edit_data): ?>
                                        <a href="admin.php?url=kelola_user_proyek" class="btn btn-secondary btn-block">
                                            <i class="fas fa-times mr-2"></i>Batal Edit
                                        </a>
                                    <?php else: ?>
                                        <button type="reset" class="btn btn-warning btn-block">
                                            <i class="fas fa-undo mr-2"></i>Reset Form
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabel Daftar User Proyek -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-users mr-2"></i>Daftar User Proyek
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead class="thead-dark">
                        <tr>
                            <th class="text-center">No</th>
                            <th>Nama Petugas</th>
                            <th>Username</th>
                            <th class="text-center">Password</th>
                            <th class="text-center">Level</th>
                            <th class="text-center">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $no = 1;
                        $query = mysqli_query($koneksi, "SELECT * FROM petugas WHERE level='proyek' ORDER BY nama_petugas ASC");
                        $total_users = mysqli_num_rows($query);

                        if ($total_users > 0) {
                            while ($data = mysqli_fetch_array($query)) {
                        ?>
                        <tr>
                            <td class="text-center"><?php echo $no++; ?></td>
                            <td class="font-weight-bold"><?php echo htmlspecialchars($data['nama_petugas']); ?></td>
                            <td>
                                <code class="text-primary"><?php echo htmlspecialchars($data['username']); ?></code>
                            </td>
                            <td class="text-center">
                                <span class="text-muted">
                                    <i class="fas fa-eye-slash mr-1"></i>
                                    <?php echo str_repeat('•', min(8, strlen($data['password']))); ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="badge badge-success px-3 py-2">
                                    <i class="fas fa-user-cog mr-1"></i><?php echo ucfirst($data['level']); ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="admin.php?url=kelola_user_proyek&edit=<?php echo $data['id_petugas']; ?>"
                                       class="btn btn-warning btn-sm" title="Edit User">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="showDeleteModal(<?php echo $data['id_petugas']; ?>, '<?php echo htmlspecialchars($data['nama_petugas']); ?>')"
                                            class="btn btn-danger btn-sm" title="Hapus User">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php
                            }
                        } else {
                        ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-center">
                                    <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                                    <h5 class="text-gray-600">Belum Ada User Proyek</h5>
                                    <p class="text-muted">Silakan tambah user proyek menggunakan form di atas.</p>
                                </div>
                            </td>
                        </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>

            <?php if ($total_users > 0): ?>
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-info-circle mr-1"></i>
                    Total: <strong><?php echo $total_users; ?></strong> user proyek terdaftar
                </small>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

</div>
<!-- /.container-fluid -->

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning mr-2"></i>Konfirmasi Hapus User
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-user-times fa-3x text-danger mb-3"></i>
                    <h5>Apakah Anda yakin?</h5>
                    <p class="text-muted">Anda akan menghapus user:</p>
                    <div class="alert alert-warning">
                        <strong id="deleteUserName"></strong>
                    </div>
                    <p class="text-muted">
                        <small>
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            Tindakan ini tidak dapat dibatalkan!
                        </small>
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-2"></i>Batal
                </button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="hapus">
                    <input type="hidden" name="id_petugas" id="deleteUserId">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash mr-2"></i>Ya, Hapus User
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function showDeleteModal(id, nama) {
    document.getElementById('deleteUserId').value = id;
    document.getElementById('deleteUserName').textContent = nama;
    $('#deleteModal').modal('show');
}

// Auto-hide alerts after 5 seconds
$(document).ready(function() {
    setTimeout(function() {
        $('.alert-dismissible').fadeOut('slow');
    }, 5000);
});
</script>
