<!DOCTYPE html>
<html lang="id">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Sistem Manajemen Proyek Arsitek - Antosa Arsitek">
    <meta name="author" content="Antosa Arsitek">
    <meta name="keywords" content="arsitek, proyek, manajemen, desain, konstruksi">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../tmp/img/favicon.ico">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title : 'Dashboard Proyek'; ?> - Antosa Arsitek">
    <meta property="og:description" content="Sistem Manajemen Proyek Arsitek">
    <meta property="og:type" content="website">

    <title><?php echo isset($page_title) ? $page_title : 'Dashboard Proyek'; ?> - Antosa Arsitek</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <!-- Custom responsive styles -->
    <style>
        /* Mobile sidebar improvements */
        @media (max-width: 768px) {
            /* Sidebar default state - hidden */
            #accordionSidebar {
                position: fixed !important;
                top: 0 !important;
                left: -14rem !important;
                width: 14rem !important;
                height: 100vh !important;
                z-index: 1050 !important;
                transition: left 0.3s ease-in-out !important;
                overflow-y: auto !important;
            }

            /* Sidebar when toggled - visible */
            #accordionSidebar.toggled {
                left: 0 !important;
            }

            /* Content wrapper adjustments */
            #content-wrapper {
                margin-left: 0 !important;
                width: 100% !important;
            }

            /* Overlay when sidebar is open */
            body.sidebar-toggled::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1040;
                display: block;
            }

            /* Prevent body scroll when sidebar is open */
            body.sidebar-toggled {
                overflow: hidden;
            }

            /* Sidebar collapse items - ensure they work properly on mobile */
            #accordionSidebar .collapse {
                position: relative !important;
                overflow: visible !important;
            }

            #accordionSidebar .collapse.show {
                display: block !important;
            }

            #accordionSidebar .collapse-inner {
                position: relative !important;
                z-index: 1 !important;
            }

            /* Ensure nav links are clickable */
            #accordionSidebar .nav-link {
                position: relative !important;
                z-index: 2 !important;
            }

            /* Fix dropdown items */
            #accordionSidebar .dropdown-item {
                position: relative !important;
                z-index: 1 !important;
                white-space: nowrap !important;
            }

            /* Improve mobile table responsiveness */
            .table-responsive {
                font-size: 0.875rem;
            }

            /* Better mobile form spacing */
            .form-group {
                margin-bottom: 1.5rem;
            }

            /* Mobile-friendly buttons */
            .btn-block {
                margin-bottom: 0.5rem;
            }
        }

        /* Desktop sidebar - ensure normal behavior */
        @media (min-width: 769px) {
            #accordionSidebar {
                position: relative !important;
                left: auto !important;
                width: 14rem !important;
            }

            #accordionSidebar.toggled {
                width: 6.5rem !important;
            }

            #content-wrapper {
                margin-left: 14rem !important;
            }

            body.sidebar-toggled #content-wrapper {
                margin-left: 6.5rem !important;
            }
        }

        /* Improve text truncation */
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Better mobile card spacing */
        @media (max-width: 576px) {
            .card {
                margin-bottom: 1rem;
            }

            .card-header {
                padding: 0.75rem 1rem;
            }

            .card-body {
                padding: 1rem;
            }
        }
    </style>

    <!-- Include functions for reusable components -->
    <?php require_once 'includes/fungsi.php'; ?>

</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">
